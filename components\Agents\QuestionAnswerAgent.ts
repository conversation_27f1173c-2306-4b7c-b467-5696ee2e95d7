/**
 * QuestionAnswerAgent.ts
 *
 * This agent provides enhanced question answering capabilities by leveraging document search,
 * logical reasoning, and contextual analysis. It's designed to be used exclusively by the PMOAgent
 * to support project management and delegation tasks.
 */

import { QueryDocumentsAgent, QueryDocumentsAgentResult } from './QueryDocumentsAgent';
import { findInternalDocumentsTool, DocumentInfo } from '../../lib/tools/findInternalDocumentsTool';
import { calculatorTool } from '../../lib/tools/calculatorTool';
import { calendarTool } from '../../lib/tools/calendarTool';
import { internetSearchTool } from '../../lib/tools/internetSearchTool';
import { TokenManagement } from '@/src/tokenTracker/tokenManagement';
import { Source } from '../../app/src/types/shared';
import { processWithGroq } from '../../lib/tools/groq-ai';
import { processWithAnthropic } from '../../lib/tools/anthropic-ai';
import { adminDb } from '../../components/firebase-admin';
import { QueryContextAnalysisSchema, safeParseJson } from '../../lib/schemas/llmResponseSchemas';

export interface QuestionAnswerAgentConfig {
  tokenManager?: TokenManagement;
  defaultModel?: string;
  defaultTemperature?: number;
  defaultMaxTokens?: number;
  maxResults?: number;
  maxQuestionsPerRequest?: number;
  followUpDepth?: number;
  memoryEnabled?: boolean;
  maxMemoryItems?: number;
}

export interface QuestionAnswerPair {
  question: string;
  answer: string;
  sources?: Source[];
  confidence?: number;
  toolsUsed?: string[];
}

export interface QuestionAnswerRequest {
  userRequest: string;
  context?: string;
  userId: string;
  category?: string;
  previousQuestions?: QuestionAnswerPair[];
  maxQuestions?: number;
  enabledTools?: {
    internetSearch?: boolean;
    calculator?: boolean;
    calendar?: boolean;
  };
  useMemory?: boolean;
  conversationId?: string;
  modelProvider?: string;
  modelName?: string;
}

export interface QuestionAnswerResult {
  success: boolean;
  questions: QuestionAnswerPair[];
  summary?: string;
  toolsUsed?: string[];
  conversationId?: string;
  error?: string;
}

export interface MemoryItem {
  id: string;
  userId: string;
  conversationId: string;
  timestamp: number;
  request: string;
  response: string;
  questions: QuestionAnswerPair[];
  context?: string;
  category?: string;
  toolsUsed?: string[];
}

export interface ContextAnalysisResult {
  explicitRequirements: string[];
  implicitRequirements: string[];
  inferredObjectives: string[];
  constraints: string[];
  stakeholders: string[];
  domainContext: string;
  knowledgeGaps: string[];
  assumptionsMade: string[];
  confidenceLevel: number; // 0-1
}

export interface ObjectiveInferenceResult {
  primaryObjective: string;
  secondaryObjectives: string[];
  businessValue: string;
  successCriteria: string[];
  risks: string[];
  confidenceInInference: number; // 0-1
}

export class QuestionAnswerAgent {
  private queryDocumentsAgent: QueryDocumentsAgent;
  private defaultModel: string;
  private defaultTemperature: number;
  private defaultMaxTokens: number;
  private maxQuestionsPerRequest: number;
  private followUpDepth: number;
  private memoryEnabled: boolean;
  private maxMemoryItems: number;

  constructor(config: QuestionAnswerAgentConfig = {}) {
    this.queryDocumentsAgent = new QueryDocumentsAgent({
      maxResults: config.maxResults || 10,
      defaultTemperature: config.defaultTemperature || 0.3,
      defaultMaxTokens: config.defaultMaxTokens || 2000,
      tokenManager: config.tokenManager
    });

    this.defaultModel = config.defaultModel || 'claude-sonnet-4-0';
    this.defaultTemperature = config.defaultTemperature || 0.3;
    this.defaultMaxTokens = config.defaultMaxTokens || 2000;
    this.maxQuestionsPerRequest = config.maxQuestionsPerRequest || 5;
    this.followUpDepth = config.followUpDepth || 2;
    this.memoryEnabled = config.memoryEnabled !== undefined ? config.memoryEnabled : true;
    this.maxMemoryItems = config.maxMemoryItems || 50;
  }

  /**
   * Analyze query context using Claude 3.7 Sonnet
   * This method determines if the query has enough context to answer effectively
   *
   * @param query - The user's query
   * @param context - Optional additional context
   * @returns Analysis result with context sufficiency and recommendations
   */
  async analyzeQueryContext(query: string, context?: string): Promise<{
    hasEnoughContext: boolean;
    missingContext: string[];
    enhancedQuery?: string;
    recommendation: string;
  }> {
    try {
      console.log(`QuestionAnswerAgent: Analyzing query context with Claude: "${query}"`);

      const prompt = `
You are an expert question answering assistant. Your task is to analyze the following query and determine if it has enough context to be answered effectively.

Query: "${query}"
${context ? `Additional Context: ${context}` : ''}

Please analyze this query and determine:
1. Does this query have enough specific context to be answered effectively? (true/false)
2. What specific context is missing? (list as bullet points)
3. How could this query be enhanced to be more effective? (provide a specific enhanced version)
4. What recommendation would you give to the user to improve their question? (be specific and helpful)

Respond in JSON format with the following structure:
{
  "hasEnoughContext": boolean,
  "missingContext": [string array of missing context elements],
  "enhancedQuery": string (improved version of the query),
  "recommendation": string (helpful guidance for the user)
}
`;

      try {
        // First try with Claude 3.7
        const response = await processWithAnthropic({
          prompt,
          model: "claude-3-7-sonnet",
          modelOptions: {
            temperature: 0.3,
            maxTokens: 1000
          }
        });

        // Use the safeParseJson function with our Zod schema
        const result = safeParseJson(QueryContextAnalysisSchema, response, {
          hasEnoughContext: true,
          missingContext: [],
          recommendation: "I'll try to answer based on your query."
        });

        // Ensure we return the correct type with default values for required fields
        return {
          hasEnoughContext: result.hasEnoughContext ?? true,
          missingContext: result.missingContext ?? [],
          enhancedQuery: result.enhancedQuery,
          recommendation: result.recommendation ?? "I'll try to answer based on your query."
        };
      } catch (claudeError) {
        console.error("Error with Claude, falling back to llama:", claudeError);

        // Fallback to llama-3.3-70b-versatile
        const fallbackResponse = await processWithGroq({
          prompt,
          model: "llama-3.3-70b-versatile",
          modelOptions: {
            temperature: 0.3,
            maxTokens: 1000
          }
        });

        try {
          // Use the safeParseJson function with our Zod schema for the fallback response
          const result = safeParseJson(QueryContextAnalysisSchema, fallbackResponse, {
            hasEnoughContext: true,
            missingContext: [],
            recommendation: "I'll try to answer based on your query."
          });

          // Ensure we return the correct type with default values for required fields
          return {
            hasEnoughContext: result.hasEnoughContext ?? true,
            missingContext: result.missingContext ?? [],
            enhancedQuery: result.enhancedQuery,
            recommendation: result.recommendation ?? "I'll try to answer based on your query."
          };
        } catch (parseError) {
          console.error("Error parsing fallback response:", parseError);
          // Default to allowing the process to proceed if we can't parse the response
          return {
            hasEnoughContext: true,
            missingContext: [],
            recommendation: "I'll try to answer based on your query."
          };
        }
      }
    } catch (error) {
      console.error("Error analyzing query context:", error);
      // Default to allowing the process to proceed if there's an error
      return {
        hasEnoughContext: true,
        missingContext: [],
        recommendation: "I'll try to answer based on your query."
      };
    }
  }

  /**
   * Main entry point for processing question-answer requests
   *
   * @param request - The question-answer request
   * @returns Question-answer result with questions and summary
   */
  async process(request: QuestionAnswerRequest): Promise<QuestionAnswerResult> {
    try {
      console.log(`QuestionAnswerAgent: Processing request "${request.userRequest}" for user ${request.userId}`);

      // Generate or use provided conversation ID
      const conversationId = request.conversationId || this.generateConversationId();

      // Track tools used
      const toolsUsed: string[] = [];

      // First, analyze the query context to see if it has enough information
      const contextAnalysis = await this.analyzeQueryContext(request.userRequest, request.context);

      // If the query doesn't have enough context, return a helpful response
      if (!contextAnalysis.hasEnoughContext) {
        return {
          success: false,
          questions: [],
          summary: `I need more specific information to effectively answer your question. ${contextAnalysis.recommendation}\n\nMissing context includes:\n${contextAnalysis.missingContext.map(item => `- ${item}`).join('\n')}`,
          error: "Insufficient context for answering question"
        };
      }

      // If we have an enhanced query, use it
      const finalRequest = {
        ...request,
        userRequest: contextAnalysis.enhancedQuery || request.userRequest
      };

      // Check memory for similar questions if enabled
      let relevantMemories: MemoryItem[] = [];
      if ((finalRequest.useMemory !== false && this.memoryEnabled) || finalRequest.useMemory === true) {
        toolsUsed.push('memory');
        relevantMemories = await this.retrieveRelevantMemories(finalRequest.userId, finalRequest.userRequest, finalRequest.category);
      }

      // Step 1: Find relevant documents
      const relevantDocuments = await this.findRelevantDocuments(finalRequest);
      if (relevantDocuments.length > 0) {
        toolsUsed.push('findInternalDocumentsTool');
      }

      // Step 2: Generate initial questions based on the request, context, and memories
      const initialQuestions = await this.generateInitialQuestions(
        finalRequest.userRequest,
        finalRequest.context,
        relevantDocuments,
        relevantMemories,
        finalRequest.maxQuestions || this.maxQuestionsPerRequest,
        { modelName: finalRequest.modelName, modelProvider: finalRequest.modelProvider }
      );

      // Step 3: Answer each question using the QueryDocumentsAgent
      const questionAnswerPairs: QuestionAnswerPair[] = [];

      for (const question of initialQuestions) {
        const answer = await this.answerQuestion(
          question,
          finalRequest.userId,
          finalRequest.category,
          finalRequest.enabledTools
        );

        questionAnswerPairs.push(answer);

        // Add tools used in this answer
        if (answer.toolsUsed) {
          for (const tool of answer.toolsUsed) {
            if (!toolsUsed.includes(tool)) {
              toolsUsed.push(tool);
            }
          }
        }
      }

      // Step 4: Generate follow-up questions based on initial answers (if needed)
      if (this.followUpDepth > 0 && questionAnswerPairs.length > 0) {
        const followUpQuestions = await this.generateFollowUpQuestions(
          finalRequest.userRequest,
          questionAnswerPairs,
          Math.min(3, finalRequest.maxQuestions || this.maxQuestionsPerRequest),
          { modelName: finalRequest.modelName, modelProvider: finalRequest.modelProvider }
        );

        // Answer follow-up questions
        for (const question of followUpQuestions) {
          const answer = await this.answerQuestion(
            question,
            finalRequest.userId,
            finalRequest.category,
            finalRequest.enabledTools
          );

          questionAnswerPairs.push(answer);

          // Add tools used in this answer
          if (answer.toolsUsed) {
            for (const tool of answer.toolsUsed) {
              if (!toolsUsed.includes(tool)) {
                toolsUsed.push(tool);
              }
            }
          }
        }
      }

      // Step 5: Generate a summary of all question-answer pairs
      const summary = await this.synthesizeSummary(
        finalRequest.userRequest,
        questionAnswerPairs,
        finalRequest.context,
        finalRequest
      );

      // Store in memory if enabled
      if ((finalRequest.useMemory !== false && this.memoryEnabled) || finalRequest.useMemory === true) {
        await this.storeInMemory({
          id: this.generateMemoryId(),
          userId: finalRequest.userId,
          conversationId,
          timestamp: Date.now(),
          request: finalRequest.userRequest,
          response: summary,
          questions: questionAnswerPairs,
          context: finalRequest.context,
          category: finalRequest.category,
          toolsUsed
        });
      }

      return {
        success: true,
        questions: questionAnswerPairs,
        summary,
        toolsUsed,
        conversationId
      };
    } catch (error) {
      console.error("Error in QuestionAnswerAgent:", error);
      return {
        success: false,
        questions: [],
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Find relevant documents for the request
   * This method uses the GenericDocumentContentExtractorTool when possible
   */
  private async findRelevantDocuments(request: QuestionAnswerRequest): Promise<DocumentInfo[]> {
    try {
      console.log(`QuestionAnswerAgent: Finding relevant documents for request "${request.userRequest}"`);

      // First try to find documents by category if provided
      if (request.category) {
        console.log(`QuestionAnswerAgent: Searching for documents in category "${request.category}"`);
        const categoryResult = await findInternalDocumentsTool.process({
          userId: request.userId,
          category: request.category
        });

        if (categoryResult.success && categoryResult.documents.length > 0) {
          console.log(`QuestionAnswerAgent: Found ${categoryResult.documents.length} documents in category "${request.category}"`);

          // If we have documents, try to analyze their content with the GenericDocumentContentExtractorTool
          try {
            // Import the GenericDocumentContentExtractorTool
            const { genericDocumentContentExtractorTool } = await import('../../lib/tools/genericDocumentContentExtractorTool');

            // Get raw content for each document
            const documentsWithContent = await Promise.all(
              categoryResult.documents.map(async (doc) => {
                try {
                  // Try to get document content from raw_content collection
                  const contentSnapshot = await adminDb.collection('users')
                    .doc(request.userId)
                    .collection('raw_content')
                    .doc(doc.namespace || doc.id)
                    .get();

                  if (contentSnapshot.exists) {
                    const contentData = contentSnapshot.data();
                    return {
                      ...doc,
                      content: contentData?.content || contentData?.text || ''
                    };
                  }

                  return doc;
                } catch (error) {
                  console.error(`Error getting content for document ${doc.id}:`, error);
                  return doc;
                }
              })
            );

            // Filter documents that have content
            const documentsWithActualContent = documentsWithContent.filter(doc => 'content' in doc && doc.content);

            if (documentsWithActualContent.length > 0) {
              console.log(`QuestionAnswerAgent: Found ${documentsWithActualContent.length} documents with content`);

              // Analyze each document with the GenericDocumentContentExtractorTool
              const analyzedDocuments = await Promise.all(
                documentsWithActualContent.map(async (doc) => {
                  const result = await genericDocumentContentExtractorTool.process({
                    documentContent: (doc as any).content,
                    documentTitle: doc.name,
                    documentId: doc.id,
                    userQuery: request.userRequest,
                    performVectorSimilarity: true,
                    userId: request.userId
                  });

                  return {
                    document: doc,
                    analysis: result
                  };
                })
              );

              // Sort documents by relevance score (if available)
              analyzedDocuments.sort((a, b) => {
                const scoreA = a.analysis.relevanceScore || 0;
                const scoreB = b.analysis.relevanceScore || 0;
                return scoreB - scoreA; // Sort in descending order
              });

              // Return the documents in order of relevance
              return analyzedDocuments.map(item => item.document);
            }
          } catch (error) {
            console.error("Error analyzing document content:", error);
            // Fall back to returning all documents in the category
            return categoryResult.documents;
          }

          // If we couldn't analyze the documents, return all documents in the category
          return categoryResult.documents;
        }
      }

      // If no category provided or no documents found in category, try to find documents by keywords
      console.log(`QuestionAnswerAgent: Searching for documents by keywords`);
      const keywords = this.extractKeywords(request.userRequest);
      let allDocuments: DocumentInfo[] = [];

      for (const keyword of keywords) {
        const result = await findInternalDocumentsTool.searchByPartialName(
          request.userId,
          keyword,
          request.category
        );

        // Add unique documents
        for (const doc of result) {
          if (!allDocuments.some(existingDoc => existingDoc.id === doc.id)) {
            allDocuments.push(doc);
          }
        }

        // Stop if we have enough documents
        if (allDocuments.length >= 10) {
          break;
        }
      }

      console.log(`QuestionAnswerAgent: Found ${allDocuments.length} documents by keywords`);
      return allDocuments;
    } catch (error) {
      console.error("Error finding relevant documents:", error);
      return [];
    }
  }

  /**
   * Extract keywords from the request for document search
   * This is a fallback method when vector similarity search is not available
   */
  private extractKeywords(request: string): string[] {
    // Simple keyword extraction - in a real implementation, this would be more sophisticated
    const words = request.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['what', 'when', 'where', 'which', 'who', 'why', 'how', 'does', 'is', 'are', 'was', 'were', 'will', 'would', 'should', 'could', 'can', 'have', 'has', 'had', 'been', 'being', 'that', 'this', 'these', 'those', 'there', 'their', 'they', 'them', 'then', 'than', 'with', 'without', 'about', 'for', 'from', 'into', 'onto', 'upon'].includes(word));

    // Get unique words and sort by length (descending)
    const uniqueWords = [...new Set(words)].sort((a, b) => b.length - a.length);

    // Return top 5 keywords
    return uniqueWords.slice(0, 5);
  }

  /**
   * Generate initial questions based on the request and context
   */
  private async generateInitialQuestions(
    request: string,
    context?: string,
    relevantDocuments?: DocumentInfo[],
    relevantMemories?: MemoryItem[],
    maxQuestions: number = 5,
    modelInfo?: { modelName?: string, modelProvider?: string }
  ): Promise<string[]> {
    const prompt = `
You are an expert business analyst tasked with generating logical questions to gather information needed to answer a request.

Request: ${request}

${context ? `Additional Context: ${context}\n\n` : ''}

${relevantDocuments && relevantDocuments.length > 0 ? `
Relevant Documents:
${relevantDocuments.map(doc => `- ${doc.name} (Category: ${doc.category}, Type: ${doc.type})`).join('\n')}
` : 'No specific documents identified yet.'}

${relevantMemories && relevantMemories.length > 0 ? `
Relevant Previous Conversations:
${relevantMemories.map((memory, i) => `
Memory ${i+1}:
- Request: ${memory.request}
- Response: ${memory.response.substring(0, 200)}${memory.response.length > 200 ? '...' : ''}
`).join('\n')}
` : ''}

Generate ${maxQuestions} logical questions that will help gather the information needed to provide a comprehensive answer to the request.

Your questions should:
1. Be clear, specific, and directly relevant to the request
2. Focus on gathering critical information needed to provide a complete answer
3. Address different aspects of the request to ensure comprehensive coverage
4. Be phrased in a way that can be answered with information from documents
5. Help infer the underlying objectives and requirements if they're not explicit
6. Be based on context, requirements, and inferred objectives

Provide ONLY the questions, one per line, without any explanations or numbering.
`;

    const response = await processWithGroq({
      prompt,
      model: modelInfo && modelInfo.modelName ? modelInfo.modelName : this.defaultModel,
      modelOptions: {
        temperature: 0.4,
        maxTokens: 1000
      }
    });

    // Parse questions from response
    return response
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.endsWith('?') && line.length > 10)
      .slice(0, maxQuestions);
  }

  /**
   * Answer a specific question using QueryDocumentsAgent and other tools
   */
  private async answerQuestion(
    question: string,
    userId: string,
    category?: string,
    enabledTools?: {
      internetSearch?: boolean;
      calculator?: boolean;
      calendar?: boolean;
    }
    // Removed unused modelInfo parameter
  ): Promise<QuestionAnswerPair> {
    const toolsUsed: string[] = ['queryDocumentsTool'];

    // Determine which tools might be needed for this question
    const needsCalculator = enabledTools?.calculator && this.questionNeedsCalculator(question);
    const needsCalendar = enabledTools?.calendar && this.questionNeedsCalendar(question);
    const mightNeedInternetSearch = enabledTools?.internetSearch && this.questionMightNeedInternetSearch(question);

    // First try to answer from internal documents
    let result = await this.queryDocumentsAgent.process({
      query: question,
      userId,
      category,
      useInternetSearch: false // Don't use internet search yet
    });

    // Try calculator if the question requires it, regardless of document search results
    if (needsCalculator) {
      const calcResult = await calculatorTool.process({
        expression: this.extractMathExpression(question)
      });

      if (calcResult.success) {
        result = this.enhanceResultWithCalculation(result, calcResult);
        toolsUsed.push('calculator');
      }
    }

    // Try calendar if the question requires it, regardless of document search results
    if (needsCalendar) {
      const calResult = await calendarTool.process({
        operation: this.determineCalendarOperation(question),
        date: this.extractDateFromQuestion(question)
      });

      if (calResult.success) {
        result = this.enhanceResultWithCalendarInfo(result, calResult);
        toolsUsed.push('calendar');
      }
    }

    // Try internet search if needed and document search was unsuccessful or confidence is low
    if (mightNeedInternetSearch && (!result.success || this.getConfidenceScore(result) < 0.5)) {
        const searchResult = await internetSearchTool.process({
          query: question,
          numResults: 3
        });

        if (searchResult.success) {
          result = this.enhanceResultWithInternetSearch(result, searchResult);
          toolsUsed.push('internetSearch');
        }
    }

    // Format the final answer
    return {
      question,
      answer: result.content,
      sources: result.sources,
      confidence: this.getConfidenceScore(result),
      toolsUsed
    };
  }

  /**
   * Generate follow-up questions based on initial answers
   */
  private async generateFollowUpQuestions(
    originalRequest: string,
    questionAnswerPairs: QuestionAnswerPair[],
    maxQuestions: number = 3,
    modelInfo?: { modelName?: string, modelProvider?: string }
  ): Promise<string[]> {
    const prompt = `
You are an expert business analyst tasked with generating follow-up questions based on initial answers.

Original Request: ${originalRequest}

Initial Questions and Answers:
${questionAnswerPairs.map((qa, i) => `
Question ${i+1}: ${qa.question}
Answer ${i+1}: ${qa.answer}
Confidence: ${qa.confidence ? `${(qa.confidence * 100).toFixed(0)}%` : 'Unknown'}
`).join('\n')}

Based on these initial answers, generate ${maxQuestions} follow-up questions that will:
1. Address gaps or inconsistencies in the information gathered so far
2. Explore areas that were mentioned but not fully explained
3. Dig deeper into aspects that seem particularly relevant to the original request
4. Help clarify any ambiguities in the initial answers
5. Focus on areas where the confidence was lower
6. Be based on context, requirements, and inferred objectives

Provide ONLY the questions, one per line, without any explanations or numbering.
`;

    let response;
    try {
      // First try with Claude 3.7
      response = await processWithAnthropic({
        prompt,
        model: modelInfo && modelInfo.modelName ? modelInfo.modelName : this.defaultModel,
        modelOptions: {
          temperature: 0.4,
          maxTokens: 1000
        }
      });
    } catch (claudeError) {
      console.error("Error with Claude in generateInitialQuestions, falling back to llama:", claudeError);

      // Fallback to llama-3.3-70b-versatile
      response = await processWithGroq({
        prompt,
        model: "llama-3.3-70b-versatile",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 1000
        }
      });
    }

    // Parse questions from response
    return response
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.endsWith('?') && line.length > 10)
      .slice(0, maxQuestions);
  }

  /**
   * Synthesize a summary from all question-answer pairs
   */
  private async synthesizeSummary(
    originalRequest: string,
    questionAnswerPairs: QuestionAnswerPair[],
    context?: string,
    request?: QuestionAnswerRequest
  ): Promise<string> {
    const prompt = `
You are an expert business consultant tasked with synthesizing a comprehensive answer based on a series of questions and answers.

Original Request: ${originalRequest}

${context ? `Additional Context: ${context}\n\n` : ''}

Questions and Answers:
${questionAnswerPairs.map((qa, i) => `
Question ${i+1}: ${qa.question}
Answer ${i+1}: ${qa.answer}
${qa.sources && qa.sources.length > 0 ? `Sources: ${qa.sources.map(s => s.title).join(', ')}` : ''}
`).join('\n')}

Synthesize a comprehensive, well-structured answer to the original request that:
1. Directly addresses what was asked
2. Integrates information from all the question-answer pairs
3. Prioritizes information from the most relevant and high-confidence answers
4. Provides a coherent narrative that flows logically
5. Includes specific details and examples where appropriate
6. Acknowledges any limitations or uncertainties in the information
7. Provides actionable insights or recommendations where appropriate

Your response should be informative, concise, and well-organized.
`;

    try {
      // First try with Claude 3.7
      return await processWithAnthropic({
        prompt,
        model: request && request.modelName ? request.modelName : this.defaultModel,
        modelOptions: {
          temperature: 0.3,
          maxTokens: 2000
        }
      });
    } catch (claudeError) {
      console.error("Error with Claude in synthesizeSummary, falling back to llama:", claudeError);

      // Fallback to llama-3.3-70b-versatile
      return await processWithGroq({
        prompt,
        model: "llama-3.3-70b-versatile",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 2000
        }
      });
    }
  }

  /**
   * Get confidence score from query result
   */
  private getConfidenceScore(result: QueryDocumentsAgentResult): number {
    if (!result.success) return 0;

    // If metadata has averageRelevance, use that
    if (result.metadata?.averageRelevance) {
      return result.metadata.averageRelevance;
    }

    // Check if calculator or calendar was used
    if (result.metadata?.functionCallingUsed) {
      return 0.9; // High confidence for calculator/calendar results
    }

    // If sources are available, calculate average relevance
    if (result.sources && result.sources.length > 0) {
      const relevanceSum = result.sources.reduce((sum, source) => sum + (source.relevance || 0), 0);
      return relevanceSum / result.sources.length;
    }

    // Default confidence for successful results without other indicators
    return 0.7;
  }

  /**
   * Check if a question needs calculator
   */
  private questionNeedsCalculator(question: string): boolean {
    const patterns = [
      /calculate|compute|sum|total|average|mean|median|percentage|ratio/i,
      /\d+\s*[\+\-\*\/\^]\s*\d+/,
      /how (much|many|long)|what (is|are) the (sum|total|average|mean|median)/i
    ];

    return patterns.some(pattern => pattern.test(question));
  }

  /**
   * Check if a question needs calendar
   */
  private questionNeedsCalendar(question: string): boolean {
    const patterns = [
      /date|time|day|month|year|week|calendar|schedule|holiday/i,
      /when (is|was|will)|how (long|many days)/i,
      /\d{1,2}\/\d{1,2}\/\d{2,4}|\d{4}-\d{1,2}-\d{1,2}/,
      /january|february|march|april|may|june|july|august|september|october|november|december/i
    ];

    return patterns.some(pattern => pattern.test(question));
  }

  /**
   * Check if a question might need internet search
   */
  private questionMightNeedInternetSearch(question: string): boolean {
    const patterns = [
      /current|latest|news|recent|today|online|website|internet/i,
      /market (trends|data|analysis)|industry (standards|benchmarks)/i,
      /external|outside|public|global|worldwide/i
    ];

    return patterns.some(pattern => pattern.test(question));
  }

  /**
   * Extract math expression from question
   */
  private extractMathExpression(question: string): string {
    // Simple regex-based extraction (would be more sophisticated in production)
    const mathRegex = /calculate\s+([\d\s\+\-\*\/\(\)\^\.\%]+)/i;
    const matchResult = question.match(mathRegex);
    return matchResult ? matchResult[1].trim() : question;
  }

  /**
   * Determine calendar operation from question
   */
  private determineCalendarOperation(question: string): 'getCurrentDateTime' | 'calculateDate' | 'getDayOfWeek' | 'getTimeDifference' {
    if (/what (?:day|date) is/i.test(question)) return "getDayOfWeek";
    if (/how many days (?:between|from)/i.test(question)) return "getTimeDifference";
    if (/what date is (\d+) days from/i.test(question)) return "calculateDate";
    return "getCurrentDateTime"; // Default operation
  }

  /**
   * Extract date from question
   */
  private extractDateFromQuestion(question: string): string {
    // Extract date mentions from the question
    const dateRegex = /(?:\d{1,2}\/\d{1,2}\/\d{2,4})|(?:\d{4}-\d{1,2}-\d{1,2})|(?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{2,4}/i;
    const matchResult = question.match(dateRegex);
    return matchResult ? matchResult[0] : new Date().toISOString().split('T')[0]; // Default to today
  }

  /**
   * Enhance result with calculation
   */
  private enhanceResultWithCalculation(originalResult: QueryDocumentsAgentResult, calcResult: any): QueryDocumentsAgentResult {
    // If original result was successful, append calculation
    if (originalResult.success) {
      return {
        ...originalResult,
        content: `${originalResult.content}\n\nCalculation result: ${calcResult.result}`
      };
    }
    // If original result failed, create new result with calculation
    else {
      return {
        success: true,
        content: `Based on your question, I've calculated the result: ${calcResult.result}`,
        metadata: {
          functionCallingUsed: true
        }
      };
    }
  }

  /**
   * Enhance result with calendar information
   */
  private enhanceResultWithCalendarInfo(originalResult: QueryDocumentsAgentResult, calResult: any): QueryDocumentsAgentResult {
    // If original result was successful, append calendar info
    if (originalResult.success) {
      return {
        ...originalResult,
        content: `${originalResult.content}\n\nDate information: ${calResult.result}`
      };
    }
    // If original result failed, create new result with calendar info
    else {
      return {
        success: true,
        content: `Based on your question, here's the date information: ${calResult.result}`,
        metadata: {
          functionCallingUsed: true
        }
      };
    }
  }

  /**
   * Enhance result with internet search
   */
  private enhanceResultWithInternetSearch(originalResult: QueryDocumentsAgentResult, searchResult: any): QueryDocumentsAgentResult {
    // If original result was successful but low confidence, combine results
    if (originalResult.success) {
      return {
        ...originalResult,
        content: `Based on your internal documents:\n${originalResult.content}\n\nAdditional information from the internet:\n${searchResult.content}`,
        sources: [...(originalResult.sources || []), ...(searchResult.sources || [])],
        metadata: {
          ...originalResult.metadata,
          internetSearchUsed: true
        }
      };
    }
    // If original result failed, use internet search result
    else {
      return {
        success: true,
        content: `I couldn't find information in your documents, but I found this on the internet:\n\n${searchResult.content}`,
        sources: searchResult.sources,
        metadata: { internetSearchUsed: true }
      };
    }
  }

  /**
   * Analyze context to extract explicit and implicit requirements
   */
  async analyzeContext(
    query: string,
    context?: string,
    previousExchanges?: { question: string; answer: string }[]
  ): Promise<ContextAnalysisResult> {
    const prompt = `
You are an expert business analyst tasked with analyzing a query and its context to extract requirements and infer objectives.

Query: ${query}

${context ? `Additional Context: ${context}\n\n` : ''}

${previousExchanges && previousExchanges.length > 0 ? `
Previous Exchanges:
${previousExchanges.map((e, i) => `
Q${i+1}: ${e.question}
A${i+1}: ${e.answer}
`).join('\n')}
` : ''}

Perform a detailed analysis to identify:

1. Explicit Requirements: Requirements clearly stated in the query or context
2. Implicit Requirements: Requirements that are implied but not explicitly stated
3. Inferred Objectives: The underlying objectives that the query is trying to achieve
4. Constraints: Any limitations or constraints mentioned or implied
5. Stakeholders: Individuals or groups who would be affected by or interested in this query
6. Domain Context: The business or technical domain this query relates to
7. Knowledge Gaps: Important information that is missing and needs to be clarified
8. Assumptions Made: Any assumptions you've made in your analysis
9. Confidence Level: Your confidence in this analysis (0-1)

Provide your analysis as a JSON object with these fields.
`;

    let response;
    try {
      // First try with Claude 3.7
      response = await processWithAnthropic({
        prompt,
        model: this.defaultModel,
        modelOptions: {
          temperature: 0.3,
          maxTokens: 2000
        }
      });
    } catch (claudeError) {
      console.error("Error with Claude in analyzeContext, falling back to llama:", claudeError);

      // Fallback to llama-3.3-70b-versatile
      response = await processWithGroq({
        prompt,
        model: "llama-3.3-70b-versatile",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 2000
        }
      });
    }

    try {
      return JSON.parse(response);
    } catch (error) {
      console.error("Error parsing context analysis:", error);
      return {
        explicitRequirements: [],
        implicitRequirements: [],
        inferredObjectives: [],
        constraints: [],
        stakeholders: [],
        domainContext: "Unknown",
        knowledgeGaps: [],
        assumptionsMade: [],
        confidenceLevel: 0.5
      };
    }
  }

  /**
   * Infer objectives from the query and context
   */
  async inferObjectives(
    query: string,
    contextAnalysis: ContextAnalysisResult
  ): Promise<ObjectiveInferenceResult> {
    const prompt = `
You are an expert business consultant tasked with inferring the underlying objectives behind a query.

Query: ${query}

Context Analysis:
- Explicit Requirements: ${contextAnalysis.explicitRequirements.join(', ')}
- Implicit Requirements: ${contextAnalysis.implicitRequirements.join(', ')}
- Constraints: ${contextAnalysis.constraints.join(', ')}
- Stakeholders: ${contextAnalysis.stakeholders.join(', ')}
- Domain Context: ${contextAnalysis.domainContext}

Based on this information, infer:

1. Primary Objective: The main goal the query is trying to achieve
2. Secondary Objectives: Additional goals that support the primary objective
3. Business Value: The business value that achieving these objectives would provide
4. Success Criteria: How success would be measured for these objectives
5. Risks: Potential risks or challenges in achieving these objectives
6. Confidence in Inference: Your confidence in these inferences (0-1)

Provide your analysis as a JSON object with these fields.
`;

    let response;
    try {
      // First try with Claude 3.7
      response = await processWithAnthropic({
        prompt,
        model: this.defaultModel,
        modelOptions: {
          temperature: 0.3,
          maxTokens: 2000
        }
      });
    } catch (claudeError) {
      console.error("Error with Claude in inferObjectives, falling back to llama:", claudeError);

      // Fallback to llama-3.3-70b-versatile
      response = await processWithGroq({
        prompt,
        model: "llama-3.3-70b-versatile",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 2000
        }
      });
    }

    try {
      return JSON.parse(response);
    } catch (error) {
      console.error("Error parsing objective inference:", error);
      return {
        primaryObjective: "Unknown",
        secondaryObjectives: [],
        businessValue: "Unknown",
        successCriteria: [],
        risks: [],
        confidenceInInference: 0.5
      };
    }
  }

  /**
   * Generate a unique conversation ID
   */
  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Generate a unique memory ID
   */
  private generateMemoryId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Store a memory item in the database
   */
  private async storeInMemory(memoryItem: MemoryItem): Promise<void> {
    try {
      // Store in Firestore
      await adminDb
        .collection('users')
        .doc(memoryItem.userId)
        .collection('questionAnswerMemory')
        .doc(memoryItem.id)
        .set(memoryItem);

      console.log(`Stored memory item ${memoryItem.id} for user ${memoryItem.userId}`);

      // Prune old memories if needed
      await this.pruneOldMemories(memoryItem.userId);
    } catch (error) {
      console.error("Error storing memory:", error);
    }
  }

  /**
   * Retrieve relevant memories based on the request
   */
  private async retrieveRelevantMemories(
    userId: string,
    request: string,
    category?: string
  ): Promise<MemoryItem[]> {
    try {
      // Get all memories for the user
      const memoryRef = adminDb
        .collection('users')
        .doc(userId)
        .collection('questionAnswerMemory');

      // If category is provided, filter by category
      let query = category
        ? memoryRef.where('category', '==', category)
        : memoryRef;

      // Order by timestamp (descending) and limit to 50 most recent
      query = query.orderBy('timestamp', 'desc').limit(50);

      const snapshot = await query.get();

      if (snapshot.empty) {
        return [];
      }

      // Get all memories
      const allMemories = snapshot.docs.map(doc => doc.data() as MemoryItem);

      // Find relevant memories using semantic similarity
      // This is a simplified approach - in a real implementation, you would use embeddings
      const relevantMemories = allMemories
        .filter(memory => this.isRelevantMemory(memory, request))
        .slice(0, 5); // Limit to 5 most relevant memories

      return relevantMemories;
    } catch (error) {
      console.error("Error retrieving memories:", error);
      return [];
    }
  }

  /**
   * Check if a memory is relevant to the current request
   * This is a simplified approach - in a real implementation, you would use embeddings
   */
  private isRelevantMemory(memory: MemoryItem, request: string): boolean {
    // Convert both to lowercase for case-insensitive comparison
    const memoryRequest = memory.request.toLowerCase();
    const currentRequest = request.toLowerCase();

    // Check for exact match
    if (memoryRequest === currentRequest) {
      return true;
    }

    // Check for significant word overlap
    const memoryWords = new Set(memoryRequest.split(/\s+/).filter(word => word.length > 3));
    const requestWords = new Set(currentRequest.split(/\s+/).filter(word => word.length > 3));

    // Count common words
    let commonWords = 0;
    for (const word of requestWords) {
      if (memoryWords.has(word)) {
        commonWords++;
      }
    }

    // Calculate similarity score
    const similarityScore = commonWords / Math.max(memoryWords.size, requestWords.size);

    // Return true if similarity score is above threshold
    return similarityScore > 0.3;
  }

  /**
   * Prune old memories to stay within the maximum limit
   */
  private async pruneOldMemories(userId: string): Promise<void> {
    try {
      // Get count of memories
      const memoryRef = adminDb
        .collection('users')
        .doc(userId)
        .collection('questionAnswerMemory');

      const snapshot = await memoryRef.orderBy('timestamp', 'desc').get();

      // If we have more than the maximum, delete the oldest ones
      if (snapshot.size > this.maxMemoryItems) {
        const memoriesToDelete = snapshot.docs.slice(this.maxMemoryItems);

        // Delete in batches
        const batch = adminDb.batch();
        for (const doc of memoriesToDelete) {
          batch.delete(doc.ref);
        }

        await batch.commit();
        console.log(`Pruned ${memoriesToDelete.length} old memories for user ${userId}`);
      }
    } catch (error) {
      console.error("Error pruning old memories:", error);
    }
  }
}

// Export a singleton instance for easy import
export const questionAnswerAgent = new QuestionAnswerAgent();
