'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../app/context/AuthContext';
import { AgentOutput, getAgentOutputs } from '../../lib/firebase/agentOutputs';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Skeleton } from '../ui/skeleton';
import { Clock, Download, FileText, Search, X, Play, Loader2 } from 'lucide-react';
import { Input } from '../ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { ScrollArea } from '../ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { toast } from '../ui/use-toast';

/**
 * PMO Agent Outputs Tab Component
 *
 * Displays a list of PMO agent outputs with filtering and viewing capabilities.
 */
export default function AgentOutputsTab() {
  const { user } = useAuth();
  const [outputs, setOutputs] = useState<AgentOutput[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOutput, setSelectedOutput] = useState<AgentOutput | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCreatingStrategicPlan, setIsCreatingStrategicPlan] = useState(false);

  // Fetch agent outputs from all teams for PMO projects
  useEffect(() => {
    async function fetchOutputs() {
      if (!user?.email) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch outputs from all agent types that are related to PMO projects
        const allOutputs = await Promise.all([
          getAgentOutputs(user.email, 'PMO'),
          getAgentOutputs(user.email, 'PMO_Assessment_And_Requirements'),
          getAgentOutputs(user.email, 'Marketing'),
          getAgentOutputs(user.email, 'Research'),
          getAgentOutputs(user.email, 'SoftwareDesign'),
          getAgentOutputs(user.email, 'Sales'),
          getAgentOutputs(user.email, 'BusinessAnalysis')
        ]);

        // Flatten and filter for PMO-related outputs
        const flattenedOutputs = allOutputs.flat();

        // Filter outputs that are related to PMO projects (have PMO category or metadata)
        const pmoRelatedOutputs = flattenedOutputs.filter(output =>
          output.metadata?.category?.includes('PMO -') ||
          output.metadata?.pmoId ||
          output.agentType.includes('PMO') ||
          (output.metadata?.source === 'PMO') ||
          (output.title && output.title.includes('PMO'))
        );

        setOutputs(pmoRelatedOutputs);
      } catch (err: any) {
        console.error('Error fetching PMO-related agent outputs:', err);
        setError(err.message || 'Failed to fetch PMO-related agent outputs');
      } finally {
        setLoading(false);
      }
    }

    fetchOutputs();
  }, [user]);

  // Filter outputs based on search term
  const filteredOutputs = outputs.filter(output =>
    output.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    output.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle output selection
  const handleOutputSelect = (output: AgentOutput) => {
    setSelectedOutput(output);
    setIsDialogOpen(true);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    setIsDialogOpen(false);
  };

  // Helper function to check if a category follows the PMO format pattern
  const isPMOFormatCategory = (category: string): boolean => {
    if (!category) return false;

    // Check if it matches the pattern: PMO - {title} - {id}
    // where {id} should be a UUID format
    const pmoPattern = /^PMO\s*-\s*.+\s*-\s*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return pmoPattern.test(category);
  };

  // Helper function to get the appropriate category for a PMO output
  const getPMOCategory = (output: AgentOutput, pmoId: string): string => {
    // If the output already has a category that follows the PMO format, use it
    if (output.metadata?.category && isPMOFormatCategory(output.metadata.category)) {
      return output.metadata.category;
    }

    // Otherwise, create a new category in the PMO format
    return `PMO - ${output.title} - ${pmoId}`;
  };

  // Trigger strategic plan creation by team
  const triggerStrategicPlan = async (output: AgentOutput) => {
    if (isCreatingStrategicPlan) return;

    // Extract PMO information from the output metadata
    const pmoId = output.metadata?.pmoId;
    const teamName = output.metadata?.assignedTeam || output.metadata?.teamName;
    const teamId = output.metadata?.assignedTeamId || output.metadata?.teamId;

    if (!pmoId || !teamName) {
      toast({
        title: "Cannot Create Strategic Plan",
        description: "Missing PMO ID or team information in the selected output.",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingStrategicPlan(true);

    try {
      // Get the appropriate category (reuse existing if it matches pattern, or create new)
      const categoryToUse = getPMOCategory(output, pmoId);

      const response = await fetch('/api/pmo-trigger-strategic-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pmoId,
          teamId,
          teamName,
          projectTitle: output.metadata?.recordTitle || output.title,
          projectDescription: output.metadata?.recordDescription || '',
          pmoAssessment: output.content,
          teamSelectionRationale: output.metadata?.teamSelectionRationale || '',
          priority: output.metadata?.recordPriority || 'Medium',
          category: categoryToUse,
          userId: user?.email,
          requirementsDocument: output.content
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to create strategic plan');
      }

      toast({
        title: "Strategic Plan Created",
        description: `${teamName} team has successfully created their strategic implementation plan.`,
      });

      // Refresh outputs to show the new strategic plan
      if (user?.email) {
        const allOutputs = await Promise.all([
          getAgentOutputs(user.email, 'PMO'),
          getAgentOutputs(user.email, 'PMO_Assessment_And_Requirements'),
          getAgentOutputs(user.email, 'Marketing'),
          getAgentOutputs(user.email, 'Research'),
          getAgentOutputs(user.email, 'SoftwareDesign'),
          getAgentOutputs(user.email, 'Sales'),
          getAgentOutputs(user.email, 'BusinessAnalysis')
        ]);

        const flattenedOutputs = allOutputs.flat();
        const pmoRelatedOutputs = flattenedOutputs.filter(output =>
          output.metadata?.category?.includes('PMO -') ||
          output.metadata?.pmoId ||
          output.agentType.includes('PMO') ||
          (output.metadata?.source === 'PMO') ||
          (output.title && output.title.includes('PMO'))
        );

        setOutputs(pmoRelatedOutputs);
      }

    } catch (error) {
      console.error('Error creating strategic plan:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create strategic plan. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingStrategicPlan(false);
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true });
  };

  // Render loading state
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">PMO Agent Outputs</h2>
          <Skeleton className="h-10 w-64" />
        </div>
        {[1, 2, 3].map(i => (
          <Skeleton key={i} className="h-40 w-full" />
        ))}
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-800 rounded-md text-red-800 dark:text-red-300">
        <h2 className="text-lg font-semibold mb-2">Error Loading Outputs</h2>
        <p>{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Get agent type display name and color
  const getAgentTypeInfo = (agentType: string) => {
    switch (agentType) {
      case 'PMO':
      case 'PMO_Assessment_And_Requirements':
        return { name: 'PMO', color: 'bg-purple-600' };
      case 'Marketing':
        return { name: 'Marketing', color: 'bg-blue-600' };
      case 'Research':
        return { name: 'Research', color: 'bg-green-600' };
      case 'SoftwareDesign':
        return { name: 'Software Design', color: 'bg-orange-600' };
      case 'Sales':
        return { name: 'Sales', color: 'bg-red-600' };
      case 'BusinessAnalysis':
        return { name: 'Business Analysis', color: 'bg-yellow-600' };
      default:
        return { name: agentType, color: 'bg-gray-600' };
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-white">PMO Agent Outputs</h2>
          <p className="text-sm text-gray-400 mt-1">
            All agent outputs related to PMO projects. Select an output to view details, thinking process, and generated documents.
          </p>
        </div>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search outputs..."
            className="pl-8 bg-gray-800 border-gray-700"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              className="absolute right-0 top-0 h-full aspect-square p-0"
              onClick={() => setSearchTerm('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Panel - Output List */}
        <div className="lg:col-span-1">
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Agent Outputs ({filteredOutputs.length})
            </h3>

            {filteredOutputs.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p className="text-sm">
                  {outputs.length === 0
                    ? "No PMO-related agent outputs found"
                    : "No outputs match your search criteria"}
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-[600px] overflow-y-auto pr-2">
                {filteredOutputs.map((output) => {
                  const agentInfo = getAgentTypeInfo(output.agentType);
                  return (
                    <div
                      key={output.id}
                      onClick={() => handleOutputSelect(output)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedOutput?.id === output.id
                          ? 'bg-purple-600/20 border border-purple-500'
                          : 'bg-gray-700/50 hover:bg-gray-700 border border-transparent'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium text-white ${agentInfo.color}`}>
                            {agentInfo.name}
                          </span>
                          <span className="text-xs text-gray-400">
                            {formatDate(output.createdAt)}
                          </span>
                        </div>
                      </div>
                      <h4 className="text-sm font-medium text-white mb-1 line-clamp-2">
                        {output.title}
                      </h4>
                      <p className="text-xs text-gray-400 line-clamp-2">
                        {output.content.substring(0, 100)}...
                      </p>
                      {output.fileUrl && (
                        <div className="mt-2 flex items-center text-xs text-blue-400">
                          <Download className="h-3 w-3 mr-1" />
                          PDF Available
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Output Details */}
        <div className="lg:col-span-2">
          {selectedOutput ? (
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium text-white ${getAgentTypeInfo(selectedOutput.agentType).color}`}>
                      {getAgentTypeInfo(selectedOutput.agentType).name}
                    </span>
                    <span className="text-xs text-gray-400">
                      {formatDate(selectedOutput.createdAt)}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-white">{selectedOutput.title}</h3>
                </div>
                <div className="flex space-x-2">
                  {selectedOutput.fileUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(selectedOutput.fileUrl, '_blank')}
                      className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download PDF
                    </Button>
                  )}
                  {/* Show strategic plan creation button for PMO requirements documents */}
                  {(selectedOutput.agentType.includes('PMO') &&
                    selectedOutput.title.includes('Requirements') &&
                    selectedOutput.metadata?.assignedTeam) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => triggerStrategicPlan(selectedOutput)}
                      disabled={isCreatingStrategicPlan}
                      className="bg-green-600 hover:bg-green-700 text-white border-green-600"
                    >
                      {isCreatingStrategicPlan ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-1" />
                          Create Strategic Plan
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>

              <Tabs defaultValue="content" className="w-full">
                <TabsList className="bg-gray-700">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="metadata">Metadata</TabsTrigger>
                </TabsList>

                <TabsContent value="content" className="mt-4">
                  <ScrollArea className="h-[500px] border border-gray-700 rounded-md p-4 bg-gray-900">
                    <div className="prose dark:prose-invert max-w-none">
                      {selectedOutput.content.split('\n').map((line, i) => (
                        <React.Fragment key={i}>
                          {line}
                          <br />
                        </React.Fragment>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="metadata" className="mt-4">
                  <ScrollArea className="h-[500px] border border-gray-700 rounded-md p-4 bg-gray-900">
                    {selectedOutput.metadata ? (
                      <div className="space-y-3">
                        {Object.entries(selectedOutput.metadata).map(([key, value]) => (
                          <div key={key} className="grid grid-cols-3 gap-4 py-2 border-b border-gray-700">
                            <div className="font-medium text-gray-300 capitalize">
                              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                            </div>
                            <div className="col-span-2 text-gray-400 break-words">
                              {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-400">No metadata available</p>
                    )}
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-lg p-6 flex flex-col items-center justify-center h-[600px] text-center">
              <FileText className="h-16 w-16 mb-4 opacity-20 text-purple-400" />
              <h3 className="text-lg font-medium text-white mb-2">Select an Output to View Details</h3>
              <p className="text-sm text-gray-400 max-w-md">
                Choose an agent output from the list to view its content, metadata, and any generated documents.
                This includes outputs from all teams working on PMO projects.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Document Viewer Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>{selectedOutput?.title}</DialogTitle>
            <DialogDescription className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {selectedOutput && formatDate(selectedOutput.createdAt)}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs defaultValue="content">
              <TabsList>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="h-full">
                <ScrollArea className="h-[calc(80vh-10rem)] border rounded-md p-4">
                  <div className="prose dark:prose-invert max-w-none">
                    {selectedOutput?.content.split('\n').map((line, i) => (
                      <React.Fragment key={i}>
                        {line}
                        <br />
                      </React.Fragment>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="metadata" className="h-full">
                <ScrollArea className="h-[calc(80vh-10rem)] border rounded-md p-4">
                  {selectedOutput?.metadata ? (
                    <div className="space-y-2">
                      {Object.entries(selectedOutput.metadata).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-2 py-1 border-b">
                          <div className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
                          <div className="col-span-2">{String(value)}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No metadata available</p>
                  )}
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            {selectedOutput?.fileUrl && (
              <Button
                onClick={() => window.open(selectedOutput.fileUrl, '_blank')}
              >
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            )}
            <Button variant="outline" onClick={handleDialogClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
